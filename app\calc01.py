from fastapi import Request, Depends, Form
from typing import Annotated, Optional
import math
from static.pdf.fontloader import register_fonts_on_pdf
from fpdf import FPDF
from datetime import datetime
from app.db import User
from app.users import current_user
from jinja2_fragments.fastapi import Jin<PERSON>2Blocks
templates = Jinja2Blocks(directory="templates")
from datastar_py.fastapi import DatastarStreamingResponse
from datastar_py.sse import ServerSentEventGenerator as SSE
from app.user_settings_db import get_user_settings, get_user_customers

from fastapi import APIRouter
router = APIRouter()


@router.post("/calc01_submit")
async def calc01_submit(
    request: Request,
    select_customer: Annotated[str, Form()],
    PE: Annotated[int, Form()],
    sp_flow: Annotated[int, Form()],
    sp_BOD5: Annotated[float, Form()],
    sp_COD: Annotated[float, Form()],
    sp_SS: Annotated[float, Form()],
    sp_TN: Annotated[float, Form()],
    sp_P: Annotated[float, Form()],
    safety_factor: Annotated[float, Form()],
    svi: Annotated[int, Form()],
    primary: Annotated[bool, Form()],
    sludge_storage_duration: Annotated[int, Form()],
    wwtp_type: Annotated[str, Form()],
    separate_deintrification_chamber: Annotated[bool, Form()],
    qsv: Annotated[float, Form()],
    primary_duration: Annotated[Optional[float], Form()] = 0.0,
    user: User = Depends(current_user)
):
    if not primary_duration:
        primary_duration = 0.0

    print('select_customer:', select_customer)
    print('PE:', PE)
    print('sp_flow:', sp_flow)
    print('sp_BOD5:', sp_BOD5)
    print('sp_COD:', sp_COD)
    print('sp_SS:', sp_SS)
    print('sp_TN:', sp_TN)
    print('sp_P:', sp_P)
    print('safety_factor:', safety_factor)
    print('svi:', svi)
    print('primary:', primary)
    print('primary_duration:', primary_duration)
    print('sludge_storage_duration:', sludge_storage_duration)
    print('wwtp_type:', wwtp_type)
    print('separate_deintrification_chamber:', separate_deintrification_chamber)
    print('qsv:', qsv)

    errorline:str = ''

    if primary_duration > 1:
        sp_TN = 0.9 * sp_TN

    # NOT REALLY NEEDED, SHOULD BE TAKEN CARE OF BY THE FONTEND
    if not primary:
        primary_duration = 0

    cod_bod5:float = 0.0
    cod_bod5 = sp_COD / sp_BOD5
    print('cod_bod5:', cod_bod5)
    if not (1.8 <= cod_bod5 <= 2.2):
        errorline = errorline + f'cod_bod5: <b>{cod_bod5}</b> is out of range (1.8-2.2)<br><br>'

    qd:float = 0.0
    qd = PE * sp_flow / 1000
    print('qd:',qd)

    qh:float = 0.0
    qh = qd / 10
    print('qh:',qh)

    bod5:float = 0.0
    bod5 = PE * sp_BOD5 / 1000
    print('bod5:',bod5)

    bod5_remove_prim:float = 0.0
    if primary_duration < 0.5:
        bod5_remove_prim = 0
    elif primary_duration > 1:
        bod5_remove_prim = 0.3333
    else:
        bod5_remove_prim = 0.25
    print('bod5_remove_prim:',bod5_remove_prim)

    ss_remove_prim:float = 0.0
    if primary_duration < 0.5:
        ss_remove_prim = 0
    elif primary_duration > 1:
        ss_remove_prim = 0.643
    else:
        ss_remove_prim = 0.25
    print('ss_remove_prim:',ss_remove_prim)

    prim_volume:float = 0.0
    prim_volume = qd / 18 * primary_duration
    prim_volume = math.ceil(prim_volume * 1000) / 1000
    print('prim_volume:',prim_volume)

    volume_primary_final:float = 0.0
    volume_primary_final = prim_volume + PE * 0.25 * sludge_storage_duration / 12
    volume_primary_final = math.ceil(volume_primary_final * 2) / 2
    print('volume_primary_final:',volume_primary_final)

    rs:int = 0                      # 50%-150%
    if wwtp_type == "ORG. C":
        rs = 55
    elif wwtp_type == "NITR":
        rs = 60
    elif wwtp_type == "DENITR":
        rs = 65
    elif wwtp_type == "STAB":
        rs = 75
    
    print('rs:',rs)

    cbod5:float = 0.0
    cbod5 = bod5 * (1 - bod5_remove_prim) / qd * 1000
    print('cbod5:',cbod5)

    xorgn:float = 0.0
    xorgn = 0.05 * cbod5
    print('xorgn:',xorgn)

    snh4:float = 0.0
    snh4 = 0
    print('snh4:',snh4)

    sno3_er:float = 0.0
    sno3_er = 18
    print('sno3_er:',sno3_er)

    sno3_est:float = 0.0
    sno3_est = 0.7 * sno3_er
    print('sno3_est:',sno3_est)

    sorgn:float = 0.0
    sorgn = 2
    print('sorgn:',sorgn)

    cn_iat:float = 0.0
    cn_iat = sp_TN * PE / qd
    print('cn_iat:',cn_iat)

    sno3:float = 0.0
    sno3 = cn_iat - sorgn - sno3_est - snh4 - xorgn
    print('sno3:',sno3)

    sno3_cbod5:float = 0.0
    sno3_cbod5 = sno3 / cbod5
    print('sno3_cbod5:',sno3_cbod5)
    if not (0 <= sno3_cbod5 <= 0.15):
        errorline = errorline + f'sno3_cbod5: <b>{sno3_cbod5}</b> is out of range (0-0.15)<br><br>'

    vd_vb:float = 0.0                   # 0.20-0.50
    if wwtp_type == "ORG. C":
        vd_vb = 0.0
    elif wwtp_type == "NITR":
        vd_vb = 0.0
    elif wwtp_type == "DENITR":
        vd_vb = 0.0155 * math.exp(23.109 * sno3_cbod5)
    elif wwtp_type == "STAB":
        vd_vb = 0.25
    
    print('vd_vb:',vd_vb)
    if not (0.2 <= vd_vb <= 0.5):  # PROPABLY NOT NEEDED
        errorline = errorline + f'vd_vb: <b>{vd_vb}</b> is out of range (0.2-0.5)<br><br>'

    srt:float = 0.0
    if wwtp_type == "ORG. C":
        srt = 5 * safety_factor
    elif wwtp_type == "NITR":
        srt = 8.2 * safety_factor
    elif wwtp_type == "DENITR":
        srt = max(vd_vb**2 * 32.5 - vd_vb * 2.45 + 9.495, 20)
    elif wwtp_type == "STAB":
        srt = 25
        
    print('srt:',srt)


    res_sp_SS_prim:float = 0.0
    if primary_duration < 0.5:
        res_sp_SS_prim = 70
    elif primary_duration>=1.5:
        res_sp_SS_prim = 25
    else:
        res_sp_SS_prim = 35
    print('res_sp_SS_prim:',res_sp_SS_prim)

    res_sludge_prim:float = 0.0
    if primary_duration < 0.5:
        res_sludge_prim = 1.0
    elif primary_duration >= 1.5:
        res_sludge_prim = 0.357
    else:
        res_sludge_prim = 0.5
    print('res_sludge_prim:',res_sludge_prim)

    xss_iat:float = 0.0
    xss_iat = res_sp_SS_prim * PE / qd * 0.8
    print('xss_iat:',xss_iat)

    xss_iat_cbod_iat:float = 0.0
    xss_iat_cbod_iat = xss_iat / cbod5
    print('xss_iat_cbod_iat:',xss_iat_cbod_iat)

    res_sludge:float = 0.0
    res_sludge = bod5 * (1 - bod5_remove_prim) * (0.75 + 0.6 * xss_iat_cbod_iat - ((1 -0.2) * 0.17 * 0.75 * srt * 0.81) / (1 + 0.17 * srt))
    print('res_sludge:',res_sludge)

    liquid_sludge_volume:float = 0.0
    liquid_sludge_volume = (res_sludge * (1 - rs) + ss_remove_prim * 1.15 * sp_SS * PE / 1000) / 0.03 /1000
    print('liquid_sludge_volume:',liquid_sludge_volume)

    ssbs:float = 0.0
    ssbs = 1000 / svi * 2**(1/3)
    print('ssbs:',ssbs)

    ssrs:float = 0.0
    ssrs = 0.7 * ssbs
    print('ssrs:',ssrs)

    ssat:float = 0.0
    ssat = rs * ssrs / (1 + rs)
    print('ssat:',ssat)

    mlss:float = 0.0
    mlss = ssat * 1000
    print('mlss:',mlss)

    reactor_volume:float = 0.0
    reactor_volume = srt * res_sludge / mlss * 1000
    print('reactor_volume:',reactor_volume)

    reactor_volume_final:float = 0.0
    reactor_volume_final = math.ceil(reactor_volume / 0.05) * 0.05
    print('reactor_volume_final:',reactor_volume_final)

    br:float = 0.0
    br = bod5 * (1 - bod5_remove_prim) / reactor_volume_final
    print('br:',br)

    f_m:float = 0.0
    f_m = br / mlss * 1000
    f_m = round(f_m / 0.001) * 0.001
    print('f_m:',f_m)

    qa:float = 0.0
    qa = qsv / ssat / svi
    qa = math.ceil(qa / 0.1) * 0.1
    print('qa:',qa)

    settle_tank_area:float = 0.0
    settle_tank_area = qd / 10 / qa
    print('settle_tank_area:',settle_tank_area)

    settle_tank_dia:float = 0.0
    settle_tank_dia = math.sqrt(4 * settle_tank_area / math.pi)
    settle_tank_dia = math.ceil(settle_tank_dia / 0.05) * 0.05
    print('settle_tank_dia:',settle_tank_dia)

    settle_tank_len:float = 0.0
    settle_tank_len = math.sqrt(settle_tank_area)
    settle_tank_len = math.ceil(settle_tank_len / 0.05) * 0.05
    print('settle_tank_len:',settle_tank_len)

    diffuser_distance:float = 0.0
    diffuser_distance = 0.1
    print('diffuser_distance:',diffuser_distance)

    reactor_d:float = 1.8
    reactor_d = min(max(reactor_volume**(1/3) + diffuser_distance, 1.8), 3.5)
    reactor_d = min(max(1.8, reactor_volume**(1/3) + diffuser_distance), 3.5)
    print('reactor_d:',reactor_d)

    diffuser_depth:float = 0.0
    diffuser_depth = reactor_d - diffuser_distance
    print('diffuser_depth:',diffuser_depth)

    asotr:float = 0.0
    if separate_deintrification_chamber == "false":
        if wwtp_type == "ORG. C" or wwtp_type == "NITR":
            asotr = cod_bod5 * bod5 * 0.06 / 0.8 / diffuser_depth / 0.0125 * 24 * 0.8
        elif wwtp_type == "DENITR" or wwtp_type == "STAB":
            asotr = cod_bod5 * bod5 * 0.05 / 0.65 / diffuser_depth / 0.0125 * 24 * 0.65
    else:
        asotr = cod_bod5 * bod5 * 0.05 / 0.8 / diffuser_depth / 0.0125 * 24 * 0.8
    print('asotr:',asotr)

    reactor_h:float = 0.0
    reactor_h = reactor_d + 0.5
    reactor_h = math.ceil(reactor_h / 0.05) * 0.05
    print('reactor_h:',reactor_h)

    reactor_w:float = 0.5
    reactor_w = max(math.sqrt(reactor_volume / reactor_d), reactor_d)
    reactor_w = math.ceil(reactor_w / 0.1) * 0.1
    print('reactor_w:',reactor_w)

    reactor_l:float = 0.0
    reactor_l = max((reactor_volume / reactor_d / reactor_w), reactor_w)
    reactor_l = math.ceil(reactor_l / 0.1) * 0.1
    print('reactor_l:',reactor_l)

    reactor_eff_volume:float = 0.0
    reactor_eff_volume = reactor_d * reactor_l * reactor_w
    print('reactor_eff_volume:',reactor_eff_volume)

    prim_d:float = 0.0
    prim_d = reactor_d
    print('prim_d:',prim_d)

    prim_h:float = 0.0
    prim_h = reactor_h
    print('prim_h:',prim_h)

    prim_w:float = 0.0
    prim_w = reactor_w
    print('prim_w:',prim_w)

    prim_l:float = 0.0
    prim_l = volume_primary_final / prim_d / prim_w
    prim_l = math.ceil(prim_l / 0.1) * 0.1
    print('prim_l:',prim_l)

    prim_eff_volume:float = 0.0
    prim_eff_volume = prim_d * prim_l * prim_w
    print('prim_eff_volume:',prim_eff_volume)

    if errorline.endswith('<br><br>'):
        errorline = errorline[:-8]
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv",
                                                                                "signuperrormessage": errorline,
                                                                                "errortitle": 'Value Error!'})))
        async def _():
            yield SSE.merge_signals({"signalShowContentTransition": False})
            yield SSE.merge_fragments(fragments=rendered_html_str, use_view_transition=True)
        return DatastarStreamingResponse(_())
    elif errorline == '':
        # CREATE PDF REPORT
        pdf = FPDF(unit="pt", format=(840, 1188))
        register_fonts_on_pdf(pdf)
        pdf.add_page()

        # TITLE
        pdf.set_font("NotoSans700", "", 36)
        pdf.set_text_color('#754100')  # type: ignore
        pdf.set_xy(73, 68)
        pdf.cell(
            w=684,
            h=40,
            text='(25) Title Calculation Report',
            border=0,
            align="C",
            fill=False)

        # DATE
        pdf.set_font("NotoSansCondensed600", "", 20)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(493, 131)
        pdf.cell(
            w=266,
            h=28,
            text=datetime.now().strftime('%d %B %Y'),
            border=0,
            align="R",
            fill=False)

        # CREATOR
        pdf.set_font("NotoSansCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(73, 159)
        pdf.cell(
            w=84,
            h=20,
            text="Creator",
            border=0,
            align="L",
            fill=False)

        # CREATOR NAME
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(73, 180)
        # Fetch user_settings for the current user
        user_settings = await get_user_settings(user.email) if user and hasattr(user, 'email') else None
        pdf.cell(
            w=670,
            h=32,
            text=user_settings["name"] if user_settings and "name" in user_settings else "",
            border=0,
            align="L",
            fill=False)

        # CREATOR INFO
        pdf.set_font("NotoSans300", "", 19)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(73, 212)
        # Fetch user_settings for the current user
        user_settings = await get_user_settings(user.email) if user and hasattr(user, 'email') else None
        # Use multi_cell to preserve new lines in user_settings["info"]
        info_text = user_settings["info"] if user_settings and "info" in user_settings else ""
        pdf.multi_cell(
            w=670,
            h=24,
            text=info_text,
            border=0,
            align="L",
            fill=False)

        if select_customer is not None and not select_customer == '':
            # Reciepient
            pdf.set_font("NotoSansCondensed300", "I", 19)
            pdf.set_text_color("#754100")  # type: ignore
            pdf.set_xy(73, pdf.get_y() + 18)
            pdf.cell(
                w=84,
                h=20,
                text="Reciepient",
                border=0,
                align="L",
                fill=False,
                ln=True)

            # Reciepient NAME
            pdf.set_font("NotoSans600", "", 21)
            pdf.set_text_color("#000000")  # type: ignore
            pdf.set_xy(73, pdf.get_y() + 1)
            # Fetch user customers and get the selected customer's name
            customers = await get_user_customers(user.email) if user and hasattr(user, 'email') else []
            selected_customer = next((c for c in customers if c.get("name") == select_customer), None)
            pdf.cell(
                w=670,
                h=32,
                text=selected_customer["name"] if selected_customer and "name" in selected_customer else "",
                border=0,
                align="L",
                fill=False,
                ln=True)

            # Reciepient INFO
            pdf.set_font("NotoSans300", "", 19)
            pdf.set_text_color("#000000")  # type: ignore
            pdf.set_xy(73, pdf.get_y() + 2)
            # Use multi_cell to preserve new lines in selected_customer["info"]
            info_text = selected_customer["info"] if selected_customer and "info" in selected_customer else ""
            pdf.multi_cell(
                w=670,
                h=24,
                text=info_text,
                border=0,
                align="L",
                fill=False)

        # Input
        pdf.set_font("NotoSansCondensed700", "I", 24)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(73, pdf.get_y() + 36)
        pdf.cell(
            w=670,
            h=32,
            text="Input",
            border=0,
            align="L",
            fill=False,
            ln=True)

        y1:int = int(pdf.get_y() + 10) # Inputs 1st line
        x1:int = 73 # Inputs 1st row
        y2:int = y1 + 76 # Inputs 2nd line
        x2:int = 238 # Inputs 2nd row
        y3:int = y2 + 76 # Inputs 3rd line
        x3:int = 372 # Inputs 3rd row
        x4:int = 501 # Inputs 4th row
        x5:int = 649 # Inputs 5th row
        placex:int = 0
        placey:int = 0

        # LABEL
        placex = x1
        placey = y1
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Populaton Equivalent",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(PE),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x1
        placey = y2
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Total Nitrogen",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sp_TN),
            border=0,
            align="L",
            fill=False)


        # LABEL
        placex = x2
        placey = y1
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Qin per PE",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sp_flow),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x3
        placey = y1
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="BOD₅",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sp_BOD5),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x4
        placey = y1
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="COD",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sp_COD),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x5
        placey = y1
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Suspended Solids",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sp_SS),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x2
        placey = y2
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Phosphorus",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sp_P),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x3
        placey = y2
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Safety Factor",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(safety_factor),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x4
        placey = y2
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Sludge Volume Index",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(svi),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x5
        placey = y2
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Primary Treatment",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(primary),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x2
        placey = y3
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Primary Duration",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(primary_duration),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x1
        placey = y3
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Sludge Storage Duration",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(sludge_storage_duration),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x3
        placey = y3
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Treatment Type",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(wwtp_type),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x4
        placey = y3
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="Chamber separated",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(separate_deintrification_chamber),
            border=0,
            align="L",
            fill=False)

        # LABEL
        placex = x5
        placey = y3
        pdf.set_font("NotoSansExtraCondensed300", "I", 19)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(placex, placey + 28)
        pdf.cell(
            w=130,
            h=20,
            text="qₛᵥ",
            border=0,
            align="L",
            fill=False)
        # VALUE
        pdf.set_font("NotoSans600", "", 21)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(placex, placey)
        pdf.cell(
            w=670,
            h=32,
            text=str(qsv),
            border=0,
            align="L",
            fill=False)


        y:int = int(pdf.get_y()) + 72
        # Result
        pdf.set_font("NotoSansCondensed700", "I", 24)
        pdf.set_text_color("#754100")  # type: ignore
        pdf.set_xy(73, y)
        pdf.cell(
            w=670,
            h=32,
            text="Result",
            border=0,
            align="L",
            fill=False,
            ln=True)

        y = int(pdf.get_y() + 10)
        # RESULT
        pdf.set_font("NotoSans300", "", 19)
        pdf.set_text_color("#000000")  # type: ignore
        pdf.set_xy(73, y)
        pdf.multi_cell(
            w=670,
            h=24,
            text=(
                f"cod_bod5: {cod_bod5}\n"
                f"qd: {qd}\n"
                f"qh: {qh}\n"
                f"bod5: {bod5}\n"
                f"bod5_remove_prim: {bod5_remove_prim}\n"
                f"ss_remove_prim: {ss_remove_prim}\n"
                f"prim_volume: {prim_volume}\n"
                f"volume_primary_final: {volume_primary_final}\n"
                f"rs: {rs}\n"
                f"cbod5: {cbod5}\n"
                f"xorgn: {xorgn}\n"
                f"snh4: {snh4}\n"
                f"sno3_er: {sno3_er}\n"
                f"sno3_est: {sno3_est}\n"
                f"sorgn: {sorgn}\n"
                f"cn_iat: {cn_iat}\n"
                f"sno3: {sno3}\n"
                f"sno3_cbod5: {sno3_cbod5}\n"
                f"vd_vb: {vd_vb}\n"
                f"srt: {srt}\n"
                f"res_sp_SS_prim: {res_sp_SS_prim}\n"
                f"res_sludge_prim: {res_sludge_prim}\n"
                f"res_sludge: {res_sludge}\n"
                f"liquid_sludge_volume: {liquid_sludge_volume}\n"
                f"reactor_volume: {reactor_volume}\n"
                f"reactor_volume_final: {reactor_volume_final}\n"
                f"xss_iat: {xss_iat}\n"
                f"xss_iat_cbod_iat: {xss_iat_cbod_iat}\n"
                f"ssbs: {ssbs}\n"
                f"ssrs: {ssrs}\n"
                f"ssat: {ssat}\n"
                f"mlss: {mlss}\n"
                f"br: {br}\n"
                f"f_m: {f_m}\n"
                f"qa: {qa}\n"
                f"settle_tank_area: {settle_tank_area}\n"
                f"settle_tank_dia: {settle_tank_dia}\n"
                f"settle_tank_len: {settle_tank_len}\n"
                f"diffuser_distance: {diffuser_distance}\n"
                f"reactor_d: {reactor_d}\n"
                f"diffuser_depth: {diffuser_depth}\n"
                f"asotr: {asotr}\n"
                f"reactor_h: {reactor_h}\n"
                f"reactor_w: {reactor_w}\n"
                f"reactor_l: {reactor_l}\n"
                f"reactor_eff_volume: {reactor_eff_volume}\n"
                f"prim_d: {prim_d}\n"
                f"prim_h: {prim_h}\n"
                f"prim_w: {prim_w}\n"
                f"prim_l: {prim_l}\n"
                f"prim_eff_volume: {prim_eff_volume}\n"
            ),
            border=0,
            align="L",
            fill=False)

        # --- PDF GENERATION COMPLETE ---
        from app.mail_utils import send_report_email
        from fastapi import UploadFile
        from io import BytesIO
        now = datetime.now()
        filename = f"pdf_report_{now.year}_{now.month:02d}_{now.day:02d}_{now.hour:02d}_{now.minute:02d}.pdf"
        pdf_bytes = pdf.output(dest='S')
        if isinstance(pdf_bytes, str):
            pdf_bytes = pdf_bytes.encode('latin1')
        file_obj = BytesIO(pdf_bytes)
        upload_file = UploadFile(filename=filename, file=file_obj)
        await send_report_email(user.email, upload_file, filename)

        templ = templates.get_template("calcemailsent.jinja2")
        rendered_html_str = "".join(templ.blocks["content"](templ.new_context({"request": request})))
        async def _():
            yield SSE.merge_signals({"signalShowContentTransition": True})
            yield SSE.merge_fragments(fragments=rendered_html_str, use_view_transition=True)
        return DatastarStreamingResponse(_())
    else:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv",
                                                                                "signuperrormessage": "problem with errorline neither '' nor endswith('<br><br>')",
                                                                                "errortitle": 'problem with errorline'})))
        async def _():
            yield SSE.merge_signals({"signalShowContentTransition": False})
            yield SSE.merge_fragments(fragments=rendered_html_str, use_view_transition=True)
        return DatastarStreamingResponse(_())