import os
from dotenv import load_dotenv
from pydantic import SecretStr
import secrets

load_dotenv()

SECRET = SecretStr(os.getenv("SECRET", secrets.token_urlsafe(32)))
ENVIRONMENT = "development"  # Temporarily forced to development for debugging
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

# Helper functions for environment-based configuration
def is_production() -> bool:
    return ENVIRONMENT == "production"

def is_development() -> bool:
    return ENVIRONMENT == "development"